<?php

namespace Botble\Hotel\Services;

use Botble\Hotel\Models\Room;
use Botble\Hotel\Models\RoomCategory;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;

class RoomSearchService
{
    /**
     * @var RoomCapacityService
     */
    protected $roomCapacityService;

    /**
     * RoomSearchService constructor.
     */
    public function __construct(RoomCapacityService $roomCapacityService = null)
    {
        $this->roomCapacityService = $roomCapacityService ?? new RoomCapacityService();
    }

    /**
     * Get available rooms based on adults and children count
     * 
     * @param int $adults
     * @param int $children
     * @param array $additionalFilters Additional filters like dates, etc.
     * @return Collection
     */
    public function getAvailableRooms(int $adults, int $children, array $additionalFilters = []): Collection
    {
        try {
            // Log the search parameters
            \Illuminate\Support\Facades\Log::info('Room search started', [
                'adults' => $adults,
                'children' => $children,
                'additionalFilters' => $additionalFilters
            ]);
            
            // First filter rooms based on occupancy requirements
            $matchingRooms = collect();
            
            // Get all published rooms
            $allRooms = Room::with(['category', 'currency'])
                ->where('status', 'published')
                ->get();
                
            // Filter rooms based on occupancy
            foreach ($allRooms as $room) {
                $categoryName = $room->category ? strtolower($room->category->name) : '';
                
                // Log room details for debugging
                \Illuminate\Support\Facades\Log::info('Checking room', [
                    'room_id' => $room->id,
                    'room_name' => $room->name,
                    'category' => $categoryName,
                    'adults' => $adults,
                    'children' => $children
                ]);
                
                // Use RoomCapacityService for all occupancy validation (including special cases)
                // This ensures consistency across the entire system
                
                // Skip rooms that don't match occupancy requirements using RoomCapacityService
                if (!$this->roomCapacityService->roomMatchesOccupancyRules($room, $adults, $children)) {
                    \Illuminate\Support\Facades\Log::info('Room skipped - does not match occupancy rules', [
                        'room_id' => $room->id,
                        'room_name' => $room->name,
                        'category' => $categoryName,
                        'is_2bhk' => $room->is2BHKFamilyRoom(),
                        'total_occupancy' => $adults + $children
                    ]);
                    continue;
                }
                
                $matchingRooms->push($room);
            }
            
            if ($matchingRooms->isEmpty()) {
                return collect();
            }
            
            // Apply additional filters if provided
            if (!empty($additionalFilters)) {
                $filteredRooms = collect();
                
                foreach ($matchingRooms as $room) {
                    // Check date availability if dates are provided
                    if (isset($additionalFilters['start_date']) && isset($additionalFilters['end_date'])) {
                        $startDate = $additionalFilters['start_date'];
                        $endDate = $additionalFilters['end_date'];
                        
                        $condition = [
                            'start_date' => $startDate,
                            'end_date' => $endDate,
                            'adults' => $adults,
                            'children' => $children,
                            'rooms' => $additionalFilters['rooms'] ?? 1,
                        ];
                        
                        if ($room->isAvailableAt($condition)) {
                            $filteredRooms->push($room);
                        }
                    } else {
                        $filteredRooms->push($room);
                    }
                }
                
                $matchingRooms = $filteredRooms;
            }
            
            // Log the search results for debugging
            Log::info('Room search results', [
                'adults' => $adults,
                'children' => $children,
                'room_count' => $matchingRooms->count(),
            ]);
            
            return $matchingRooms;
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error in room search: ' . $e->getMessage(), [
                'adults' => $adults,
                'children' => $children,
                'trace' => $e->getTraceAsString(),
            ]);
            
            // Return empty collection on error
            return collect();
        }
    }



    /**
     * Get room types that match the given adults and children count
     * 
     * @param int $adults
     * @param int $children
     * @return array
     */
    public function getMatchingRoomTypes(int $adults, int $children): array
    {
        // Delegate to the RoomCapacityService
        return $this->roomCapacityService->getMatchingRoomTypes($adults, $children);
    }
}