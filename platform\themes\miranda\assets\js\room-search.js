// Function to check if a combination is allowed for 2BHK
function is2BHKCombinationAllowed(adults, children) {
    // Convert to integers to ensure proper comparison
    adults = parseInt(adults) || 0;
    children = parseInt(children) || 0;
    const total = adults + children;
    
    // For 2BHK: at least 1 adult, maximum 7 people total
    return adults >= 1 && total <= 7;
}

// Update client-side filtering for 2BHK rooms
function filterRoomsByOccupancy(rooms, adults, children) {
    console.log('Filtering rooms by occupancy:', {adults, children, rooms});
    adults = parseInt(adults) || 0;
    children = parseInt(children) || 0;
    const totalGuests = adults + children;
    
    return rooms.filter(function(room) {
        // Check both room name and category for family/2BHK keywords
        const isFamilyOr2BHKRoom = (
            (room.category && (
                room.category.toLowerCase().includes('family') ||
                room.category.toLowerCase().includes('2bhk') || 
                room.category.toLowerCase().includes('2 bhk') ||
                room.category.toLowerCase().includes('two bhk') ||
                room.category.toLowerCase().includes('2 bedroom')
            )) ||
            (room.name && (
                room.name.toLowerCase().includes('family') ||
                room.name.toLowerCase().includes('2bhk') || 
                room.name.toLowerCase().includes('2 bhk') ||
                room.name.toLowerCase().includes('two bhk') ||
                room.name.toLowerCase().includes('2 bedroom')
            ))
        );
        
        // Special case for family/2BHK rooms - OVERRIDE admin panel values completely
        if (isFamilyOr2BHKRoom) {
            console.log('Family/2BHK room detected:', room.name);
            
            // HARDCODED VALUES: minimum 1 adult, maximum 7 people total
            const MIN_ADULTS = 1;
            const MAX_TOTAL = 7;
            
            // CRITICAL: Ignore admin panel settings completely
            const isValid = adults >= MIN_ADULTS && totalGuests <= MAX_TOTAL;
            
            console.log('Family/2BHK validation result:', {
                adults: adults,
                children: children,
                total: totalGuests,
                minAdults: MIN_ADULTS,
                maxTotal: MAX_TOTAL,
                adminMaxAdults: room.max_adults, // For debugging only
                adminMaxChildren: room.max_children, // For debugging only
                isValid: isValid
            });
            
            if (!isValid) {
                console.warn('Family/2BHK room hidden due to occupancy limits:', room.name);
            }
            
            return isValid;
        }
        
        // Regular room filtering logic using admin panel values
        return (adults <= room.max_adults && children <= room.max_children);
    });
}

// Debug the room filtering
console.log('Room search parameters:', {
    adults: $('#adults').val(),
    children: $('#children').val(),
    total: parseInt($('#adults').val()) + parseInt($('#children').val())
});






