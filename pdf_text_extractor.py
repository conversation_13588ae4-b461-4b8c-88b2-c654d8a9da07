#!/usr/bin/env python3
"""
PDF Text Extractor to Excel
Extracts text from PDF and creates an Excel file
"""

import pandas as pd
import sys
import os

def create_excel_from_text(text_content, output_file="extracted_pdf_content.xlsx"):
    """
    Create an Excel file from extracted text content
    """
    try:
        # Split text into lines
        lines = text_content.strip().split('\n')
        
        # Remove empty lines
        lines = [line.strip() for line in lines if line.strip()]
        
        # Create DataFrame
        df = pd.DataFrame({
            'Line_Number': range(1, len(lines) + 1),
            'Content': lines
        })
        
        # Save to Excel
        df.to_excel(output_file, index=False, engine='openpyxl')
        print(f"✅ Excel file created: {output_file}")
        print(f"📊 Total lines extracted: {len(lines)}")
        
        return output_file
        
    except Exception as e:
        print(f"❌ Error creating Excel file: {e}")
        return None

def manual_text_input():
    """
    Allow manual text input for Excel creation
    """
    print("=== PDF Text to Excel Converter ===")
    print("\nSince automatic PDF extraction failed, please:")
    print("1. Open the PDF in your browser")
    print("2. Select all text (Ctrl+A)")
    print("3. Copy the text (Ctrl+C)")
    print("4. Paste the text below and press Enter twice when done:")
    print("\n" + "="*50)
    
    lines = []
    print("Paste your text here (press Enter twice to finish):")
    
    while True:
        try:
            line = input()
            if line == "" and len(lines) > 0 and lines[-1] == "":
                break
            lines.append(line)
        except EOFError:
            break
    
    # Remove the last empty line
    if lines and lines[-1] == "":
        lines.pop()
    
    text_content = '\n'.join(lines)
    
    if text_content.strip():
        output_file = create_excel_from_text(text_content)
        if output_file:
            print(f"\n🎉 Success! Your Excel file is ready: {output_file}")
    else:
        print("❌ No text content provided.")

if __name__ == "__main__":
    # Check if text is provided as argument
    if len(sys.argv) > 1:
        text_content = ' '.join(sys.argv[1:])
        create_excel_from_text(text_content)
    else:
        manual_text_input()
