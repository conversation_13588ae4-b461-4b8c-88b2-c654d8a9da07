/**
 * Get room booking parameters from request
 */
public static function getRoomBookingParams(): array
{
    $startDate = request()->query('start_date', now()->format('Y-m-d'));
    $endDate = request()->query('end_date', now()->addDay()->format('Y-m-d'));
    
    // CRITICAL FIX: Ensure adults and children are properly cast to integers
    // Use 1 adult as default (minimum required for any booking)
    $adults = (int)request()->query('adults', 1);
    $children = (int)request()->query('children', 0);
    $rooms = (int)request()->query('rooms', 1);
    
    // Add debug logging to verify parameter parsing
    \Illuminate\Support\Facades\Log::debug('HotelHelper::getRoomBookingParams', [
        'start_date' => $startDate,
        'end_date' => $endDate,
        'adults' => $adults,
        'children' => $children,
        'rooms' => $rooms,
        'raw_adults' => request()->query('adults'),
        'raw_children' => request()->query('children')
    ]);

    return [$startDate, $endDate, $adults, $children, $rooms];
}

